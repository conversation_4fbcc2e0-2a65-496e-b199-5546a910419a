using System;
using System.Linq.Expressions;
using MethodCache.Core.Configuration;

namespace MethodCache.Core
{
    public interface IServiceConfiguration<T>
    {
        IMethodConfiguration Method(Expression<Action<T>> method);
    }

    public interface IMethodCacheConfiguration
    {
        void RegisterMethod<T>(Expression<Action<T>> method, string methodId, string? groupName);

        IServiceConfiguration<T> ForService<T>();

        void DefaultDuration(TimeSpan duration);
        void DefaultKeyGenerator<TGenerator>() where TGenerator : ICacheKeyGenerator, new();
        IGroupConfiguration ForGroup(string groupName);
    }

    /// <summary>
    /// Interface for method registration functionality.
    /// Provides a contract for registering cached methods with the configuration.
    /// </summary>
    public interface ICacheMethodRegistry
    {
        /// <summary>
        /// Registers cached methods with the configuration.
        /// </summary>
        /// <param name="config">The method cache configuration to register methods with.</param>
        void RegisterMethods(IMethodCacheConfiguration config);
    }

    /// <summary>
    /// Provides method registration functionality for the cache system.
    /// This class serves as a stub for IDE support before source generation.
    /// The actual implementation is generated by the MethodCacheGenerator.
    /// </summary>
    public static class CacheMethodRegistry
    {
        private static ICacheMethodRegistry? _registry;

        /// <summary>
        /// Sets the registry implementation. This is called by the source generator.
        /// </summary>
        /// <param name="registry">The registry implementation.</param>
        public static void SetRegistry(ICacheMethodRegistry registry)
        {
            _registry = registry;
        }

        /// <summary>
        /// Registers cached methods with the configuration.
        /// If no registry is available (no cached methods found), this method does nothing.
        /// </summary>
        /// <param name="config">The method cache configuration to register methods with.</param>
        public static void Register(IMethodCacheConfiguration config)
        {
            _registry?.RegisterMethods(config);
        }
    }
}

