using System;
using System.Threading.Tasks;
using MethodCache.Core.Configuration;

namespace MethodCache.Core
{
    public class NoOpCacheManager : ICacheManager
    {
        public Task<T> GetOrCreateAsync<T>(string methodName, object[] args, Func<Task<T>> factory, CacheMethodSettings settings, ICacheKeyGenerator keyGenerator, bool requireIdempotent)
        {
            // Always execute the factory, effectively disabling caching
            return factory();
        }

        public Task InvalidateByTagsAsync(params string[] tags)
        {
            // No operation for invalidation
            return Task.CompletedTask;
        }
    }
}
