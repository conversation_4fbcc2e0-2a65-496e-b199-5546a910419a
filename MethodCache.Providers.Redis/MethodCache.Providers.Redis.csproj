<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <PackageId>MethodCache.Providers.Redis</PackageId>
    <PackageDescription>Redis distributed cache provider for MethodCache</PackageDescription>
    <PackageVersion>1.0.0</PackageVersion>
    <Authors>MethodCache</Authors>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="MessagePack" Version="3.1.4" />
    <PackageReference Include="Polly" Version="8.6.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MethodCache.Core\MethodCache.Core.csproj" />
    <ProjectReference Include="..\MethodCache.HybridCache\MethodCache.HybridCache.csproj" />
  </ItemGroup>

</Project>